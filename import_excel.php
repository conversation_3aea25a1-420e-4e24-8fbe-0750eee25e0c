<?php
require_once 'db.php';

// Configuration
ini_set('memory_limit', '256M');
set_time_limit(300); // 5 minutes

class ExcelImporter {
    private $pdo;
    private $secteurs = [];
    private $equipements = [];
    private $personnes = [];
    private $typeMapping = [
        'situation dangereuse' => 3,
        'premier soin' => 5,
        'premier soins' => 5,
        'presqu\'accident' => 2,
        'presque accident' => 2,
        'accident' => 1,
        'accident de trajet' => 4
    ];

    private $contractMapping = [
        'cdd' => 'CDD',
        'cdi' => 'CDI',
        'client' => 'Client',
        'intérimaire' => 'Intérimaire',
        'intrimaire' => 'Intérimaire',
        'multiple' => 'Multiple',
        'prestataire' => 'Prestataire',
        'stagiaire' => 'Stagiaire'
    ];

    public function __construct() {
        $this->pdo = getPDO();
        $this->loadReferenceData();
    }

    private function loadReferenceData() {
        // Charger les secteurs
        $stmt = $this->pdo->query("SELECT id, nomSecteur FROM SECTEUR");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->secteurs[strtolower($row['nomSecteur'])] = $row['id'];
        }

        // Charger les équipements
        $stmt = $this->pdo->query("SELECT id, nomEquipement FROM EQUIPEMENT");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->equipements[strtolower($row['nomEquipement'])] = $row['id'];
        }

        // Charger les personnes
        $stmt = $this->pdo->query("SELECT id, prenomPerso, nomPerso FROM PERSONNE");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $fullName = strtolower($row['prenomPerso'] . ' ' . $row['nomPerso']);
            $this->personnes[$fullName] = $row['id'];
        }
    }

    public function importFromCSV($csvFile) {
        $results = [
            'success' => 0,
            'errors' => [],
            'warnings' => []
        ];

        if (!file_exists($csvFile)) {
            throw new Exception("Fichier CSV non trouvé: $csvFile");
        }

        $handle = fopen($csvFile, 'r');
        if (!$handle) {
            throw new Exception("Impossible d'ouvrir le fichier CSV");
        }

        // Lire l'en-tête
        $header = fgetcsv($handle, 0, ';');
        if (!$header) {
            throw new Exception("Impossible de lire l'en-tête du fichier CSV");
        }

        $lineNumber = 1;
        while (($data = fgetcsv($handle, 0, ';')) !== FALSE) {
            $lineNumber++;

            try {
                if (count($data) < 10) {
                    $results['warnings'][] = "Ligne $lineNumber: Données incomplètes, ignorée";
                    continue;
                }

                $this->importAccident($data, $lineNumber, $results);
                $results['success']++;

            } catch (Exception $e) {
                $results['errors'][] = "Ligne $lineNumber: " . $e->getMessage();
            }
        }

        fclose($handle);
        return $results;
    }

    private function importAccident($data, $lineNumber, &$results) {
        // Extraction des données
        $dateStr = trim($data[0]);
        $typeStr = trim($data[1]);
        $secteurStr = trim($data[2]);
        $equipementStr = trim($data[3]);
        $personnesStr = trim($data[4]);
        $contractStr = trim($data[5]);
        $causeStr = trim($data[6]);
        $partieBlesseeStr = trim($data[7]);
        $arretTravailStr = trim($data[8]);
        $joursArretStr = trim($data[9]);

        // Validation et conversion de la date
        $dateAccident = $this->parseDate($dateStr);
        if (!$dateAccident) {
            throw new Exception("Date invalide: $dateStr");
        }

        // Mapping du type d'accident
        $typeAccident = $this->mapType($typeStr);
        if (!$typeAccident) {
            throw new Exception("Type d'accident non reconnu: $typeStr");
        }

        // Recherche du secteur
        $secteurId = $this->findSecteur($secteurStr);

        // Recherche de l'équipement
        $equipementId = $this->findEquipement($equipementStr);

        // Parsing des personnes
        $personnes = $this->parsePersonnes($personnesStr, $contractStr, $partieBlesseeStr, $joursArretStr);

        // Commencer une transaction
        $this->pdo->beginTransaction();

        try {
            // Insérer l'accident
            $stmt = $this->pdo->prepare("
                INSERT INTO ACCIDENT
                (typeAccident, idEquipement, dateAccident, causeAccident, secteurAccident)
                VALUES (?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $typeAccident,
                $equipementId,
                $dateAccident,
                $causeStr ?: null,
                $secteurId
            ]);

            $accidentId = $this->pdo->lastInsertId();

            // Insérer les personnes liées
            foreach ($personnes as $personne) {
                $this->insertAccidentPersonne($accidentId, $personne);
            }

            $this->pdo->commit();

        } catch (Exception $e) {
            $this->pdo->rollback();
            throw $e;
        }
    }

    private function parseDate($dateStr) {
        if (empty($dateStr) || $dateStr === '/') {
            return null;
        }

        // Format attendu: dd/mm/yyyy
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $dateStr, $matches)) {
            $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $year = $matches[3];
            return "$year-$month-$day";
        }

        return null;
    }

    private function mapType($typeStr) {
        $typeKey = strtolower(trim($typeStr));
        return $this->typeMapping[$typeKey] ?? null;
    }

    private function findSecteur($secteurStr) {
        if (empty($secteurStr)) {
            return null;
        }

        $secteurKey = strtolower(trim($secteurStr));
        return $this->secteurs[$secteurKey] ?? null;
    }

    private function findEquipement($equipementStr) {
        if (empty($equipementStr)) {
            return null;
        }

        $equipementKey = strtolower(trim($equipementStr));
        return $this->equipements[$equipementKey] ?? null;
    }

    private function parsePersonnes($personnesStr, $contractStr, $partieBlesseeStr, $joursArretStr) {
        $personnes = [];

        if (empty($personnesStr)) {
            return $personnes;
        }

        // Séparer les personnes par différents délimiteurs
        $separators = [',', '&', ' et ', '/'];
        $personnesList = [$personnesStr];

        foreach ($separators as $sep) {
            $newList = [];
            foreach ($personnesList as $item) {
                $newList = array_merge($newList, explode($sep, $item));
            }
            $personnesList = $newList;
        }

        // Nettoyer et traiter chaque personne
        foreach ($personnesList as $personneStr) {
            $personneStr = trim($personneStr);
            if (empty($personneStr)) {
                continue;
            }

            $personne = [
                'idPersonne' => null,
                'personneLibre' => null,
                'typeContrat' => $this->mapContract($contractStr),
                'partieBlessée' => ($partieBlesseeStr && $partieBlesseeStr !== '/') ? $partieBlesseeStr : null,
                'joursArrets' => $this->parseJoursArret($joursArretStr)
            ];

            // Rechercher la personne dans la base
            $personneId = $this->findPersonne($personneStr);
            if ($personneId) {
                $personne['idPersonne'] = $personneId;
            } else {
                $personne['personneLibre'] = $personneStr;
            }

            $personnes[] = $personne;
        }

        return $personnes;
    }

    private function findPersonne($personneStr) {
        $personneKey = strtolower(trim($personneStr));
        return $this->personnes[$personneKey] ?? null;
    }

    private function mapContract($contractStr) {
        if (empty($contractStr)) {
            return null;
        }

        $contractKey = strtolower(trim($contractStr));
        return $this->contractMapping[$contractKey] ?? null;
    }

    private function parseJoursArret($joursStr) {
        if (empty($joursStr) || $joursStr === '/') {
            return null;
        }

        $jours = intval($joursStr);
        return $jours > 0 ? $jours : null;
    }

    private function insertAccidentPersonne($accidentId, $personne) {
        $stmt = $this->pdo->prepare("
            INSERT INTO ACCIDENT_PERSONNE
            (idAccident, idPersonne, personneLibre, typeContrat, partieBlessée, joursArrets)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $accidentId,
            $personne['idPersonne'],
            $personne['personneLibre'],
            $personne['typeContrat'],
            $personne['partieBlessée'],
            $personne['joursArrets']
        ]);
    }
}

// Interface web simple
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csvFile'])) {
    try {
        $uploadedFile = $_FILES['csvFile'];

        if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
            throw new Exception("Erreur lors de l'upload du fichier");
        }

        $importer = new ExcelImporter();
        $results = $importer->importFromCSV($uploadedFile['tmp_name']);

        echo json_encode([
            'success' => true,
            'results' => $results
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Excel - Accidents</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">Import des données d'accidents depuis Excel</h1>

            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">Instructions :</h3>
                <ol class="list-decimal list-inside text-sm text-blue-700 space-y-1">
                    <li>Convertissez votre fichier Excel en CSV avec le délimiteur point-virgule (;)</li>
                    <li>Assurez-vous que les colonnes sont dans l'ordre : Date, Type d'évènement, Service concerné, Où ?, Qui ?, Type de contrat, Comment ?, Partie blessée, Arrêt de travail, Nb de jours d'arrêt</li>
                    <li>Les dates doivent être au format dd/mm/yyyy</li>
                    <li>Les personnes multiples peuvent être séparées par , & et ou /</li>
                </ol>
            </div>

            <form id="importForm" enctype="multipart/form-data" class="space-y-4">
                <div>
                    <label for="csvFile" class="block text-sm font-medium text-gray-700 mb-2">
                        Fichier CSV à importer
                    </label>
                    <input
                        type="file"
                        id="csvFile"
                        name="csvFile"
                        accept=".csv"
                        class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        required
                    >
                </div>

                <button
                    type="submit"
                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Importer les données
                </button>
            </form>

            <div id="results" class="mt-6 hidden"></div>
        </div>
    </div>

    <script>
        document.getElementById('importForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const resultsDiv = document.getElementById('results');

            // Afficher un indicateur de chargement
            resultsDiv.innerHTML = `
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                        <span class="text-blue-700">Import en cours...</span>
                    </div>
                </div>
            `;
            resultsDiv.classList.remove('hidden');

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const results = data.results;
                    let html = `
                        <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                            <h3 class="font-semibold text-green-800 mb-2">Import terminé avec succès !</h3>
                            <p class="text-green-700">Accidents importés : ${results.success}</p>
                    `;

                    if (results.warnings.length > 0) {
                        html += `
                            <div class="mt-3">
                                <h4 class="font-medium text-yellow-800">Avertissements :</h4>
                                <ul class="list-disc list-inside text-sm text-yellow-700 mt-1">
                                    ${results.warnings.map(w => `<li>${w}</li>`).join('')}
                                </ul>
                            </div>
                        `;
                    }

                    if (results.errors.length > 0) {
                        html += `
                            <div class="mt-3">
                                <h4 class="font-medium text-red-800">Erreurs :</h4>
                                <ul class="list-disc list-inside text-sm text-red-700 mt-1">
                                    ${results.errors.map(e => `<li>${e}</li>`).join('')}
                                </ul>
                            </div>
                        `;
                    }

                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <h3 class="font-semibold text-red-800 mb-2">Erreur lors de l'import</h3>
                            <p class="text-red-700">${data.error}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `
                    <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <h3 class="font-semibold text-red-800 mb-2">Erreur technique</h3>
                        <p class="text-red-700">${error.message}</p>
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
