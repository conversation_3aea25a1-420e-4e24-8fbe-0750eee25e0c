<?php
/**
 * Script simple pour convertir manuellement les données d'exemple
 * en format CSV pour tester l'import
 */

// Données d'exemple basées sur votre fichier Excel
$sampleData = [
    [
        'Date' => '22/04/2025',
        'Type d\'évènement' => 'Situation dangereuse',
        'Service concerné' => 'Logistique',
        'Où ? Lieu / poste de travail' => 'Réception',
        'Qui ?' => 'Philippe PICHON',
        'Type de contrat' => 'CDD',
        'Comment ? Circonstances de l\'incident' => 'Réception d\'une barre de matière beaucoup trop longue (au lieu des 3m max). Le stockage a été balisé par Philippe immédiatement et la situation remontée au service HSE et achat',
        'Partie blessée' => '/',
        'Arrêt de travail' => '/',
        'Nb de jours d\'arrêt' => '/'
    ],
    [
        'Date' => '23/04/2025',
        'Type d\'évènement' => 'Premier soin',
        'Service concerné' => 'Prestataire',
        'Où ? Lieu / poste de travail' => 'Laboratoire',
        'Qui ?' => 'Prestataire TRESCAL',
        'Type de contrat' => 'Prestataire',
        'Comment ? Circonstances de l\'incident' => 'Légère coupure à la main droite en serrant une vis qui a rippée',
        'Partie blessée' => 'Main',
        'Arrêt de travail' => '/',
        'Nb de jours d\'arrêt' => '/'
    ],
    [
        'Date' => '31/07/2024',
        'Type d\'évènement' => 'Presque accident',
        'Service concerné' => 'Laboratoire',
        'Où ? Lieu / poste de travail' => 'DN900',
        'Qui ?' => 'Francis LECHARTIER / Fabien HOURRIER',
        'Type de contrat' => 'Intérimaire',
        'Comment ? Circonstances de l\'incident' => 'A cause d\'une mauvaise coordination entre les 2 opérateurs à la sortie du jumper du caisson, celui-ci à failli chuter au sol',
        'Partie blessée' => '/',
        'Arrêt de travail' => '/',
        'Nb de jours d\'arrêt' => '/'
    ]
];

function generateCSV($data, $filename = 'accidents_sample.csv') {
    // En-têtes
    $headers = [
        'Date',
        'Type d\'évènement',
        'Service concerné',
        'Où ? Lieu / poste de travail',
        'Qui ?',
        'Type de contrat',
        'Comment ? Circonstances de l\'incident',
        'Partie blessée',
        'Arrêt de travail',
        'Nb de jours d\'arrêt'
    ];
    
    // Créer le contenu CSV
    $csvContent = '';
    
    // Ajouter les en-têtes
    $csvContent .= implode(';', $headers) . "\n";
    
    // Ajouter les données
    foreach ($data as $row) {
        $csvRow = [];
        foreach ($headers as $header) {
            $csvRow[] = isset($row[$header]) ? $row[$header] : '';
        }
        $csvContent .= implode(';', $csvRow) . "\n";
    }
    
    return $csvContent;
}

// Si on demande le téléchargement
if (isset($_GET['download'])) {
    $csvContent = generateCSV($sampleData);
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="accidents_sample.csv"');
    header('Content-Length: ' . strlen($csvContent));
    
    echo $csvContent;
    exit;
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur CSV d'exemple</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">Générateur CSV d'exemple pour test d'import</h1>
            
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">Données d'exemple :</h3>
                <p class="text-sm text-blue-700">
                    Ce script génère un fichier CSV avec les données d'exemple que vous avez fournies, 
                    formatées correctement pour tester l'outil d'import.
                </p>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Aperçu des données :</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Service</th>
                                <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Lieu</th>
                                <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Qui</th>
                                <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Contrat</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sampleData as $row): ?>
                            <tr class="border-b">
                                <td class="px-4 py-2 text-sm"><?= htmlspecialchars($row['Date']) ?></td>
                                <td class="px-4 py-2 text-sm"><?= htmlspecialchars($row['Type d\'évènement']) ?></td>
                                <td class="px-4 py-2 text-sm"><?= htmlspecialchars($row['Service concerné']) ?></td>
                                <td class="px-4 py-2 text-sm"><?= htmlspecialchars($row['Où ? Lieu / poste de travail']) ?></td>
                                <td class="px-4 py-2 text-sm"><?= htmlspecialchars($row['Qui ?']) ?></td>
                                <td class="px-4 py-2 text-sm"><?= htmlspecialchars($row['Type de contrat']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="flex space-x-4">
                <a 
                    href="?download=1" 
                    class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                    📥 Télécharger le CSV d'exemple
                </a>
                
                <a 
                    href="import_excel.php" 
                    class="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    → Aller à l'outil d'import
                </a>
            </div>
            
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 class="font-semibold text-yellow-800 mb-2">Instructions pour votre fichier Excel :</h3>
                <ol class="list-decimal list-inside text-sm text-yellow-700 space-y-1">
                    <li>Ouvrez votre fichier "Suivi des évènements sécurité.xlsx" dans Excel</li>
                    <li>Sélectionnez toutes les données (Ctrl+A)</li>
                    <li>Fichier → Enregistrer sous → Choisir "CSV (délimité par des points-virgules) (*.csv)"</li>
                    <li>Enregistrez le fichier</li>
                    <li>Utilisez ce fichier CSV dans l'outil d'import</li>
                </ol>
            </div>
            
            <div class="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <h3 class="font-semibold text-gray-800 mb-2">Problèmes courants et solutions :</h3>
                <ul class="list-disc list-inside text-sm text-gray-700 space-y-1">
                    <li><strong>Personnes multiples :</strong> Séparées par , & et ou / (ex: "Francis LECHARTIER / Fabien HOURRIER")</li>
                    <li><strong>Dates :</strong> Format dd/mm/yyyy (ex: "22/04/2025")</li>
                    <li><strong>Champs vides :</strong> Utilisez "/" ou laissez vide</li>
                    <li><strong>Types d'événements :</strong> "Situation dangereuse", "Premier soin", "Presque accident", etc.</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
