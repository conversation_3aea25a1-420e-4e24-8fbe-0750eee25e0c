<?php
require_once 'vendor/autoload.php'; // Si vous utilisez Composer pour PhpSpreadsheet

use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Script pour convertir le fichier Excel en CSV avec le bon format
 * Utilise PhpSpreadsheet pour lire le fichier Excel
 */

function convertExcelToCsv($excelFile, $csvFile) {
    try {
        // Charger le fichier Excel
        $spreadsheet = IOFactory::load($excelFile);
        $worksheet = $spreadsheet->getActiveSheet();
        
        // Ouvrir le fichier CSV en écriture
        $csvHandle = fopen($csvFile, 'w');
        if (!$csvHandle) {
            throw new Exception("Impossible de créer le fichier CSV");
        }
        
        // Définir l'en-tête CSV
        $header = [
            'Date',
            'Type d\'évènement', 
            'Service concerné',
            'Où ? Lieu / poste de travail',
            'Qui ?',
            'Type de contrat',
            'Comment ? Circonstances de l\'incident',
            'Partie blessée',
            'Arrêt de travail',
            'Nb de jours d\'arrêt'
        ];
        
        // Écrire l'en-tête
        fputcsv($csvHandle, $header, ';');
        
        // Parcourir les lignes du fichier Excel (en commençant à la ligne 2 pour ignorer l'en-tête)
        $highestRow = $worksheet->getHighestRow();
        
        for ($row = 2; $row <= $highestRow; $row++) {
            $rowData = [];
            
            // Lire les 10 colonnes (A à J)
            for ($col = 'A'; $col <= 'J'; $col++) {
                $cellValue = $worksheet->getCell($col . $row)->getCalculatedValue();
                
                // Traitement spécial pour les dates
                if ($col === 'A' && !empty($cellValue)) {
                    // Si c'est un timestamp Excel, le convertir
                    if (is_numeric($cellValue)) {
                        $date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($cellValue);
                        $cellValue = $date->format('d/m/Y');
                    }
                }
                
                $rowData[] = $cellValue ?? '';
            }
            
            // Écrire la ligne dans le CSV
            fputcsv($csvHandle, $rowData, ';');
        }
        
        fclose($csvHandle);
        return true;
        
    } catch (Exception $e) {
        throw new Exception("Erreur lors de la conversion : " . $e->getMessage());
    }
}

// Interface web pour la conversion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excelFile'])) {
    try {
        $uploadedFile = $_FILES['excelFile'];
        
        if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
            throw new Exception("Erreur lors de l'upload du fichier");
        }
        
        // Générer un nom de fichier CSV temporaire
        $csvFile = tempnam(sys_get_temp_dir(), 'accidents_') . '.csv';
        
        // Convertir le fichier
        convertExcelToCsv($uploadedFile['tmp_name'], $csvFile);
        
        // Télécharger le fichier CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="accidents_import.csv"');
        header('Content-Length: ' . filesize($csvFile));
        
        readfile($csvFile);
        unlink($csvFile); // Supprimer le fichier temporaire
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Convertir Excel en CSV</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">Convertir Excel en CSV pour l'import</h1>
            
            <?php if (isset($error)): ?>
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h3 class="font-semibold text-red-800 mb-2">Erreur :</h3>
                    <p class="text-red-700"><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>
            
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">Instructions :</h3>
                <ol class="list-decimal list-inside text-sm text-blue-700 space-y-1">
                    <li>Sélectionnez votre fichier Excel "Suivi des évènements sécurité.xlsx"</li>
                    <li>Le script va automatiquement convertir le fichier en CSV avec le délimiteur point-virgule (;)</li>
                    <li>Téléchargez le fichier CSV généré</li>
                    <li>Utilisez ensuite ce fichier CSV dans l'outil d'import</li>
                </ol>
            </div>

            <form method="POST" enctype="multipart/form-data" class="space-y-4">
                <div>
                    <label for="excelFile" class="block text-sm font-medium text-gray-700 mb-2">
                        Fichier Excel à convertir
                    </label>
                    <input 
                        type="file" 
                        id="excelFile" 
                        name="excelFile" 
                        accept=".xlsx,.xls"
                        class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        required
                    >
                </div>

                <button 
                    type="submit" 
                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Convertir en CSV
                </button>
            </form>
            
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 class="font-semibold text-yellow-800 mb-2">Note :</h3>
                <p class="text-sm text-yellow-700">
                    Si vous n'avez pas PhpSpreadsheet installé, vous pouvez convertir manuellement votre fichier Excel en CSV :
                    <br>1. Ouvrez le fichier dans Excel
                    <br>2. Fichier → Enregistrer sous → CSV (délimité par des points-virgules)
                    <br>3. Utilisez ce fichier CSV dans l'outil d'import
                </p>
            </div>
            
            <div class="mt-4 text-center">
                <a href="import_excel.php" class="text-blue-600 hover:text-blue-800 underline">
                    → Aller à l'outil d'import
                </a>
            </div>
        </div>
    </div>
</body>
</html>
