<!-- Modal d'ajout d'accident -->
<div class="fixed inset-0 z-50 hidden overflow-y-auto" id="addAccidentModal" aria-labelledby="addAccidentModalLabel" aria-hidden="true">
  <div class="flex items-center justify-center min-h-screen p-4 text-center sm:p-0">
    <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>

    <!-- Modal content -->
    <div class="relative inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white rounded-lg shadow-xl">
      <form id="addAccidentForm" class="space-y-4">
        <div class="flex justify-between items-center pb-3 border-b border-gray-200">
          <h3 class="text-lg font-medium leading-6 text-gray-900" id="addAccidentModalLabel">
            <i class="fas fa-plus-circle text-primary-600 mr-2"></i>Nouvel accident
          </h3>
          <button type="button" class="text-gray-400 hover:text-gray-500" data-bs-dismiss="modal" aria-label="Fermer">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Main content in two columns -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Left Column -->
          <div class="space-y-6">
            <!-- Informations générales de l'accident -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                Informations générales
              </h4>
              <div class="space-y-4">
                <!-- Date -->
                <div>
                  <label for="dateAccident" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
                  <input type="date" name="dateAccident" id="dateAccident" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" required>
                </div>

                <!-- Type -->
                <div>
                  <label for="typeAccident" class="block text-sm font-medium text-gray-700 mb-1">Type d'événement *</label>
                  <select name="typeAccident" id="typeAccident" class="custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" required>
                    <option value="">Sélectionner...</option>
                    <option value="1">🔴 Accident</option>
                    <option value="4">🔴 Accident de trajet</option>
                    <option value="5">🔵 Premier soins</option>
                    <option value="2">🟡 Presque accident</option>
                    <option value="3">🟢 Situation dangereuse</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Équipement et lieu -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-cogs text-blue-500 mr-2"></i>
                Équipement et lieu
              </h4>
              <div class="space-y-4">
                <!-- Équipement -->
                <div>
                  <label for="equipementText" class="block text-sm font-medium text-gray-700 mb-1">Équipement concerné</label>
                  <input
                    type="text"
                    id="equipementText"
                    list="equipements-options"
                    placeholder="Rechercher un équipement"
                    class="custom-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    autocomplete="off"
                  >
                  <input type="hidden" name="idEquipement" id="idEquipement">
                </div>

                <!-- Secteur de l'accident -->
                <div>
                  <label for="secteurAccident" class="block text-sm font-medium text-gray-700 mb-1">Secteur de l'accident</label>
                  <select name="secteurAccident" id="secteurAccident" class="custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    <option value="">Sélectionner...</option>
                  </select>
                  <p class="text-xs text-gray-500 mt-1">
                    <i class="fas fa-info-circle mr-1"></i>
                    Se remplit automatiquement selon l'équipement
                  </p>
                </div>
              </div>
            </div>

            <!-- Cause et Remarques -->
            <div class="bg-yellow-50 p-4 rounded-lg">
              <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-clipboard-list text-yellow-500 mr-2"></i>
                Détails supplémentaires
              </h4>
              <div class="space-y-4">
                <!-- Cause -->
                <div>
                  <label for="causeAccident" class="block text-sm font-medium text-gray-700 mb-1">Cause</label>
                  <textarea name="causeAccident" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>

                <!-- Remarques -->
                <div>
                  <label for="remarquesAccident" class="block text-sm font-medium text-gray-700 mb-1">Remarques</label>
                  <textarea name="remarquesAccident" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column -->
          <div class="space-y-6">
            <!-- Personnes impliquées -->
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="flex justify-between items-center mb-4">
                <h4 class="text-md font-medium text-gray-900">
                  <i class="fas fa-users text-green-500 mr-2"></i>
                  Personnes impliquées
                </h4>
                <button type="button" id="addPersonneBtn" class="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                  <i class="fas fa-plus mr-1"></i> Ajouter une personne
                </button>
              </div>

              <div id="personnesContainer">
                <!-- Les personnes seront ajoutées ici dynamiquement -->
              </div>

              <p class="text-xs text-gray-500 mt-2">
                <i class="fas fa-info-circle mr-1"></i>
                Vous pouvez ajouter plusieurs personnes impliquées dans l'accident. Laissez vide si aucune personne n'est concernée.
              </p>
            </div>

            <!-- GMAO -->
            <div class="bg-purple-50 p-4 rounded-lg">
              <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-link text-purple-500 mr-2"></i>
                Liaison GMAO
              </h4>
              <div>
                <label for="GMAO" class="block text-sm font-medium text-gray-700 mb-1">ID GMAO (optionnel)</label>
                <input
                  type="text"
                  name="GMAO"
                  id="GMAO"
                  list="gmao-options"
                  placeholder="Rechercher ou saisir un ID GMAO"
                  class="custom-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" data-bs-dismiss="modal">
            Annuler
          </button>
          <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-save mr-1"></i> Ajouter
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  // Variables pour gérer les personnes
  let personneCounter = 0;

  // Initialize modal
  document.addEventListener('DOMContentLoaded', function() {
    // Show modal when button is clicked
    document.getElementById('addAccidentBtn').addEventListener('click', function() {
      const modal = document.getElementById('addAccidentModal');
      modal.classList.remove('hidden');

      // Réinitialiser le formulaire et les personnes
      resetPersonnesContainer();
    });

    // Hide modal when close button is clicked
    document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(button => {
      button.addEventListener('click', function() {
        const modal = this.closest('.fixed');
        modal.classList.add('hidden');
      });
    });

    // Gestionnaire pour ajouter une personne
    document.getElementById('addPersonneBtn').addEventListener('click', function() {
      addPersonneRow();
    });

    // Test: Vérifier que le formulaire est bien présent
    const form = document.getElementById('addAccidentForm');
    if (form) {
      console.log('✅ Formulaire addAccidentForm trouvé dans le modal');

      // Ajouter un gestionnaire de test pour debug
      form.addEventListener('submit', function(e) {
        console.log('🔥 SUBMIT EVENT CAPTURED IN MODAL!');
        // Ne pas empêcher l'événement ici, laisser main.js le gérer
      });
    } else {
      console.error('❌ Formulaire addAccidentForm NON trouvé!');
    }
  });

  // Fonction pour réinitialiser le conteneur des personnes
  function resetPersonnesContainer() {
    const container = document.getElementById('personnesContainer');
    container.innerHTML = '';
    personneCounter = 0;

    // Ajouter une première personne par défaut
    addPersonneRow();
  }

  // Fonction pour ajouter une ligne de personne
  function addPersonneRow() {
    const container = document.getElementById('personnesContainer');
    const personneId = personneCounter++;

    const personneRow = document.createElement('div');
    personneRow.className = 'personne-row bg-white p-4 border border-gray-200 rounded-lg mb-3';
    personneRow.setAttribute('data-personne-id', personneId);

    personneRow.innerHTML = `
      <div class="flex justify-between items-start mb-3">
        <h5 class="text-sm font-medium text-gray-700">
          <i class="fas fa-user mr-1"></i>
          Personne ${personneId + 1}
        </h5>
        ${personneId > 0 ? `
          <button type="button" class="remove-personne-btn text-red-500 hover:text-red-700 focus:outline-none" data-personne-id="${personneId}">
            <i class="fas fa-trash-alt"></i>
          </button>
        ` : ''}
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Choix du type de personne -->
        <div class="md:col-span-2">
          <div class="flex space-x-4">
            <label class="flex items-center">
              <input type="radio" name="personneType_${personneId}" value="existante" class="mr-2" checked onchange="togglePersonneInputs(${personneId})">
              <span class="text-sm">Personne existante</span>
            </label>
            <label class="flex items-center">
              <input type="radio" name="personneType_${personneId}" value="libre" class="mr-2" onchange="togglePersonneInputs(${personneId})">
              <span class="text-sm">Personne libre</span>
            </label>
          </div>
        </div>

        <!-- Personne existante -->
        <div class="personne-existante-${personneId}">
          <label class="block text-sm font-medium text-gray-700 mb-1">Personne</label>
          <input
            type="text"
            id="personneText_${personneId}"
            list="personnes-options"
            placeholder="Rechercher une personne"
            class="custom-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            autocomplete="off"
            onchange="updatePersonneId(${personneId})"
          >
          <input type="hidden" name="personnes[${personneId}][idPersonne]" id="idPersonne_${personneId}">
        </div>

        <!-- Personne libre -->
        <div class="personne-libre-${personneId}" style="display: none;">
          <label class="block text-sm font-medium text-gray-700 mb-1">Nom de la personne</label>
          <input
            type="text"
            name="personnes[${personneId}][personneLibre]"
            placeholder="Nom de la personne en texte libre"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
        </div>

        <!-- Type de contrat -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Type de contrat</label>
          <select name="personnes[${personneId}][typeContrat]" class="custom-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
            <option value="">Sélectionner...</option>
            <option value="CDD">CDD</option>
            <option value="CDI">CDI</option>
            <option value="Client">Client</option>
            <option value="Intérimaire">Intérimaire</option>
            <option value="Multiple">Multiple</option>
            <option value="Prestataire">Prestataire</option>
            <option value="Stagiaire">Stagiaire</option>
          </select>
        </div>

        <!-- Jours d'arrêt -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Jours d'arrêt</label>
          <input
            type="number"
            name="personnes[${personneId}][joursArrets]"
            min="0"
            placeholder="0"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
        </div>

        <!-- Partie blessée -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-1">Partie blessée (optionnel)</label>
          <input
            type="text"
            name="personnes[${personneId}][partieBlessée]"
            placeholder="Ex: Main droite, Genou gauche..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
        </div>
      </div>
    `;

    container.appendChild(personneRow);

    // Ajouter les gestionnaires d'événements pour la suppression
    const removeBtn = personneRow.querySelector('.remove-personne-btn');
    if (removeBtn) {
      removeBtn.addEventListener('click', function() {
        removePersonneRow(personneId);
      });
    }
  }

  // Fonction pour basculer entre personne existante et libre
  function togglePersonneInputs(personneId) {
    const existanteDiv = document.querySelector(`.personne-existante-${personneId}`);
    const libreDiv = document.querySelector(`.personne-libre-${personneId}`);
    const radioExistante = document.querySelector(`input[name="personneType_${personneId}"][value="existante"]`);

    if (radioExistante.checked) {
      existanteDiv.style.display = 'block';
      libreDiv.style.display = 'none';
    } else {
      existanteDiv.style.display = 'none';
      libreDiv.style.display = 'block';
    }
  }

  // Fonction pour mettre à jour l'ID de la personne sélectionnée
  function updatePersonneId(personneId) {
    const input = document.getElementById(`personneText_${personneId}`);
    const hiddenInput = document.getElementById(`idPersonne_${personneId}`);
    const selectedText = input.value;

    // Chercher l'ID correspondant dans les options
    const options = document.querySelectorAll('#personnes-options option');
    let found = false;

    for (const option of options) {
      if (option.value === selectedText) {
        const id = option.getAttribute('data-id');
        hiddenInput.value = id;
        found = true;
        break;
      }
    }

    if (!found) {
      hiddenInput.value = '';
    }
  }

  // Fonction pour supprimer une ligne de personne
  function removePersonneRow(personneId) {
    const row = document.querySelector(`[data-personne-id="${personneId}"]`);
    if (row) {
      row.remove();
    }

    // Renommer les personnes restantes
    renumberPersonnes();
  }

  // Fonction pour renuméroter les personnes après suppression
  function renumberPersonnes() {
    const rows = document.querySelectorAll('.personne-row');
    rows.forEach((row, index) => {
      const title = row.querySelector('h5');
      if (title) {
        title.innerHTML = `<i class="fas fa-user mr-1"></i>Personne ${index + 1}`;
      }
    });
  }

  // Gestionnaire pour l'équipement (auto-remplissage du secteur)
  document.addEventListener('DOMContentLoaded', function() {
    const equipementInput = document.getElementById('equipementText');
    if (equipementInput) {
      equipementInput.addEventListener('input', function() {
        const selectedText = this.value;
        const options = document.querySelectorAll('#equipements-options option');
        let found = false;

        for (const option of options) {
          if (option.value === selectedText) {
            const id = option.getAttribute('data-id');
            document.getElementById('idEquipement').value = id;

            // Auto-remplir le secteur de l'accident si pas déjà sélectionné
            if (typeof autoFillSecteurFromEquipement === 'function') {
              autoFillSecteurFromEquipement(id, 'secteurAccident');
            }

            found = true;
            break;
          }
        }

        if (!found) {
          document.getElementById('idEquipement').value = '';
        }
      });
    }
  });
</script>
