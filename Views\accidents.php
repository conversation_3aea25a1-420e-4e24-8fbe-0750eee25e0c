<?php if (empty($accidents)): ?>
<div class="bg-white rounded-lg shadow-sm p-8 text-center border border-gray-200">
  <div class="flex flex-col items-center justify-center">
    <i class="fas fa-clipboard-list text-gray-300 text-5xl mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-1">Aucun accident trouvé</h3>
    <p class="text-gray-500">Aucun accident ne correspond à vos critères de recherche.</p>
  </div>
</div>
<?php else: ?>

<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
  <div class="overflow-x-auto">
    <div class="max-h-[65vh] overflow-y-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50 sticky top-0 z-10">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Personne</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Équipement</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Partie blessée</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Cause</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Jours d'arrêt</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">GMAO</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
        <?php foreach ($accidents as $a): ?>
          <tr class="hover:bg-gray-50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">
                <?= htmlspecialchars(date('d/m/Y', strtotime($a['dateAccident']))) ?>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">
                <?php
                $nombrePersonnes = (int)($a['nombrePersonnes'] ?? 0);
                $hasPersonne = (!empty($a['prenomPerso']) && !empty($a['nomPerso'])) || !empty($a['personneLibre']);
                ?>

                <?php if ($nombrePersonnes === 0): ?>
                  <!-- Aucune personne impliquée -->
                  <span class="text-gray-500 italic">Aucune personne</span>

                <?php elseif ($nombrePersonnes === 1): ?>
                  <!-- Une seule personne - affichage complet -->
                  <?php if (!empty($a['prenomPerso']) && !empty($a['nomPerso'])): ?>
                    <?= htmlspecialchars($a['prenomPerso'] . ' ' . $a['nomPerso']) ?>
                    <?php if (!empty($a['secteurPersonne'])): ?>
                    <span class="text-xs text-gray-500 block">
                      Secteur: <?= htmlspecialchars($a['secteurPersonne']) ?>
                    </span>
                    <?php endif; ?>
                  <?php elseif (!empty($a['personneLibre'])): ?>
                    <?= htmlspecialchars($a['personneLibre']) ?>
                    <span class="text-xs text-gray-500 block">Personne libre</span>
                  <?php endif; ?>

                  <?php if (!empty($a['typeContrat'])): ?>
                  <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full inline-block mt-1">
                    <?= htmlspecialchars($a['typeContrat']) ?>
                  </span>
                  <?php endif; ?>

                  <?php if (!empty($a['partieBlessée'])): ?>
                  <span class="text-xs text-red-600 block mt-1">
                    Partie blessée: <?= htmlspecialchars($a['partieBlessée']) ?>
                  </span>
                  <?php endif; ?>

                  <?php if (!empty($a['joursArrets']) && $a['joursArrets'] > 0): ?>
                  <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full inline-block mt-1">
                    <?= htmlspecialchars($a['joursArrets']) ?> jour<?= $a['joursArrets'] > 1 ? 's' : '' ?> d'arrêt
                  </span>
                  <?php endif; ?>

                <?php else: ?>
                  <!-- Plusieurs personnes - affichage condensé -->
                  <div class="flex items-center space-x-2">
                    <div>
                      <?php if (!empty($a['prenomPerso']) && !empty($a['nomPerso'])): ?>
                        <?= htmlspecialchars($a['prenomPerso'] . ' ' . $a['nomPerso']) ?>
                      <?php elseif (!empty($a['personneLibre'])): ?>
                        <?= htmlspecialchars($a['personneLibre']) ?>
                      <?php else: ?>
                        Personne 1
                      <?php endif; ?>
                    </div>
                    <button
                      type="button"
                      class="show-all-personnes-btn inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors cursor-pointer"
                      data-accident-id="<?= $a['id'] ?>"
                      title="Voir toutes les personnes impliquées"
                    >
                      +<?= $nombrePersonnes - 1 ?>
                    </button>
                  </div>

                  <!-- Informations de la première personne -->
                  <div class="mt-1">
                    <?php if (!empty($a['typeContrat'])): ?>
                    <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full inline-block">
                      <?= htmlspecialchars($a['typeContrat']) ?>
                    </span>
                    <?php endif; ?>

                    <?php if (!empty($a['joursArrets']) && $a['joursArrets'] > 0): ?>
                    <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full inline-block ml-1">
                      <?= htmlspecialchars($a['joursArrets']) ?> jour<?= $a['joursArrets'] > 1 ? 's' : '' ?>
                    </span>
                    <?php endif; ?>
                  </div>

                  <div class="text-xs text-gray-500 mt-1">
                    <?= $nombrePersonnes ?> personne<?= $nombrePersonnes > 1 ? 's' : '' ?> impliquée<?= $nombrePersonnes > 1 ? 's' : '' ?>
                  </div>
                <?php endif; ?>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">
                <?= htmlspecialchars($a['nomEquipement'] ?? '-') ?>
                <?php if (!empty($a['secteurEquipement'])): ?>
                <span class="text-xs text-gray-500 block">
                  Secteur équipement: <?= htmlspecialchars($a['secteurEquipement']) ?>
                </span>
                <?php endif; ?>
                <?php if (!empty($a['secteurAccident']) && $a['secteurAccident'] != $a['secteurEquipement']): ?>
                <span class="text-xs text-blue-600 block">
                  Secteur accident: <?= htmlspecialchars($a['secteurAccident']) ?>
                </span>
                <?php endif; ?>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center">
              <?php
              $typeClass = '';
              $typeLabel = '';

              switch ((int)$a['typeAccident']) {
                case 1: // Accident
                  $typeClass = 'bg-red-100 text-red-800';
                  $typeLabel = 'Accident';
                  break;
                case 4: // Accident de trajet
                  $typeClass = 'bg-red-100 text-red-800';
                  $typeLabel = 'Accident de trajet';
                  break;
                case 5: // Premier soins
                  $typeClass = 'bg-blue-100 text-blue-800';
                  $typeLabel = 'Premier soins';
                  break;
                case 2: // Presque accident
                  $typeClass = 'bg-yellow-100 text-yellow-800';
                  $typeLabel = 'Presque accident';
                  break;
                case 3: // Situation dangereuse
                  $typeClass = 'bg-green-100 text-green-800';
                  $typeLabel = 'Situation dangereuse';
                  break;
                default:
                  $typeClass = 'bg-gray-100 text-gray-800';
                  $typeLabel = 'Non défini';
              }
              ?>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $typeClass ?>">
                <?= htmlspecialchars($typeLabel) ?>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center">
              <div class="text-sm text-gray-900">
                <?= htmlspecialchars($a['partieBlessée'] ?? '-') ?>
              </div>
            </td>
            <td class="px-6 py-4 text-center">
              <div class="text-sm text-gray-900 max-w-xs break-words">
                <?= htmlspecialchars($a['causeAccident']) ?>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center">
              <div class="text-sm text-gray-900 font-medium">
                <?= $a['joursArrets'] > 0 ? htmlspecialchars($a['joursArrets']) : '-' ?>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center">
              <?php if (!empty($a['GMAO'])): ?>
                <a href="https://frscmgmao.scmlemans.com/GMAO/detailDemande.php?id=<?= htmlspecialchars($a['GMAO']) ?>"
                   target="_blank"
                   class="gmao-link px-2 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-primary-100 text-primary-800 hover:bg-primary-200 transition-colors"
                   title="Ouvrir la GMAO dans un nouvel onglet">
                  <span><?= htmlspecialchars($a['GMAO']) ?></span>
                  <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                </a>
              <?php else: ?>
                <span class="text-sm text-gray-500"></span>
              <?php endif; ?>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button
                  class="text-primary-600 hover:text-primary-900 edit-btn"
                  data-id="<?= $a['id'] ?>"
                  data-bs-toggle="modal"
                  data-bs-target="#editAccidentModal"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  class="text-red-600 hover:text-red-900 delete-btn"
                  data-id="<?= $a['id'] ?>"
                >
                  <i class="fas fa-trash-alt"></i>
                </button>
                <?php if (!empty($a['remarquesAccident'])): ?>
                <button
                  class="text-gray-600 hover:text-gray-900"
                  data-tippy-content="<?= htmlspecialchars($a['remarquesAccident']) ?>"
                >
                  <i class="fas fa-info-circle"></i>
                </button>
                <?php endif; ?>
              </div>
            </td>
          </tr>
        <?php endforeach; ?>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Modal pour afficher toutes les personnes d'un accident -->
<div id="personnesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <!-- Header -->
      <div class="flex justify-between items-center pb-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">
          <i class="fas fa-users mr-2 text-blue-500"></i>
          Personnes impliquées dans l'accident
        </h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="closePersonnesModal()">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- Contenu -->
      <div class="mt-4">
        <div id="personnesModalContent" class="space-y-4">
          <!-- Le contenu sera chargé dynamiquement -->
        </div>
      </div>

      <!-- Footer -->
      <div class="flex justify-end pt-4 border-t mt-4">
        <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="closePersonnesModal()">
          Fermer
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  // Update accident count
  document.getElementById('accident-count').textContent = '<?= count($accidents) ?>';

  // Fonction pour ouvrir le modal des personnes
  function openPersonnesModal(accidentId) {
    const modal = document.getElementById('personnesModal');
    const content = document.getElementById('personnesModalContent');

    // Afficher le modal
    modal.classList.remove('hidden');

    // Afficher un loader
    content.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i><p class="text-gray-500 mt-2">Chargement...</p></div>';

    // Charger les données
    fetch(`Ajax/get_accident_personnes.php?idAccident=${accidentId}`)
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          content.innerHTML = '<div class="text-center py-4 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Erreur lors du chargement</p></div>';
          return;
        }

        if (data.length === 0) {
          content.innerHTML = '<div class="text-center py-4 text-gray-500"><i class="fas fa-user-slash text-2xl"></i><p class="mt-2">Aucune personne impliquée</p></div>';
          return;
        }

        // Générer le HTML pour chaque personne
        let html = '';
        data.forEach((personne, index) => {
          html += generatePersonneCard(personne, index + 1);
        });

        content.innerHTML = html;
      })
      .catch(error => {
        console.error('Erreur:', error);
        content.innerHTML = '<div class="text-center py-4 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Erreur lors du chargement</p></div>';
      });
  }

  // Fonction pour fermer le modal des personnes
  function closePersonnesModal() {
    const modal = document.getElementById('personnesModal');
    modal.classList.add('hidden');
  }

  // Fonction pour générer le HTML d'une carte personne
  function generatePersonneCard(personne, numero) {
    const nomComplet = personne.prenomPerso && personne.nomPerso
      ? `${personne.prenomPerso} ${personne.nomPerso}`
      : personne.personneLibre || 'Personne non identifiée';

    const typePersonne = personne.prenomPerso && personne.nomPerso ? 'Personne existante' : 'Personne libre';

    return `
      <div class="bg-gray-50 p-4 rounded-lg border">
        <div class="flex items-start justify-between mb-3">
          <h4 class="text-md font-medium text-gray-900">
            <i class="fas fa-user mr-2 text-blue-500"></i>
            Personne ${numero}
          </h4>
          <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
            ${typePersonne}
          </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-700">Nom</p>
            <p class="text-sm text-gray-900">${nomComplet}</p>
            ${personne.secteurPersonne ? `<p class="text-xs text-gray-500">Secteur: ${personne.secteurPersonne}</p>` : ''}
          </div>

          <div>
            <p class="text-sm font-medium text-gray-700">Type de contrat</p>
            <p class="text-sm text-gray-900">${personne.typeContrat || '-'}</p>
          </div>

          <div>
            <p class="text-sm font-medium text-gray-700">Partie blessée</p>
            <p class="text-sm text-gray-900">${personne.partieBlessée || '-'}</p>
          </div>

          <div>
            <p class="text-sm font-medium text-gray-700">Jours d'arrêt</p>
            <p class="text-sm text-gray-900">
              ${personne.joursArrets && personne.joursArrets > 0
                ? `${personne.joursArrets} jour${personne.joursArrets > 1 ? 's' : ''}`
                : '0 jour'}
            </p>
          </div>
        </div>
      </div>
    `;
  }

  // Gestionnaire d'événements pour les boutons "+X"
  document.addEventListener('DOMContentLoaded', function() {
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('show-all-personnes-btn') || e.target.closest('.show-all-personnes-btn')) {
        const btn = e.target.classList.contains('show-all-personnes-btn') ? e.target : e.target.closest('.show-all-personnes-btn');
        const accidentId = btn.getAttribute('data-accident-id');
        openPersonnesModal(accidentId);
      }
    });

    // Fermer le modal en cliquant à l'extérieur
    document.getElementById('personnesModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closePersonnesModal();
      }
    });
  });
</script>
<?php endif; ?>
